/**
 * 图片URL处理工具函数
 */

/**
 * 获取完整的图片URL
 * 如果图片URL不是以http://或https://开头，则自动加上后端服务器地址
 * @param imageUrl 图片URL
 * @param defaultImage 默认图片路径（可选）
 * @returns 完整的图片URL
 */
export function getFullImageUrl(imageUrl?: string, defaultImage?: string): string {
  // 如果没有图片URL，使用默认图片
  if (!imageUrl || imageUrl.trim() === '') {
    return defaultImage || '/static/images/default-avatar.png'
  }

  // 如果已经是完整的URL（以http://或https://开头），直接返回
  if (imageUrl.startsWith('http://') || imageUrl.startsWith('https://')) {
    return imageUrl
  }

  // 如果是相对路径，加上后端服务器地址
  const baseUrl = import.meta.env.VITE_SERVER_BASEURL || ''
  
  // 确保baseUrl以/结尾，imageUrl不以/开头，避免双斜杠
  const normalizedBaseUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl
  const normalizedImageUrl = imageUrl.startsWith('/') ? imageUrl : `/${imageUrl}`
  
  return `${normalizedBaseUrl}${normalizedImageUrl}`
}

/**
 * 获取用户头像URL
 * 专门用于处理用户头像的URL
 * @param headerImg 用户头像字段
 * @returns 完整的头像URL
 */
export function getUserAvatarUrl(headerImg?: string): string {
  return getFullImageUrl(headerImg, '/static/images/default-avatar.png')
}

/**
 * 获取文件URL
 * 通用的文件URL处理函数
 * @param fileUrl 文件URL
 * @param defaultFile 默认文件路径（可选）
 * @returns 完整的文件URL
 */
export function getFullFileUrl(fileUrl?: string, defaultFile?: string): string {
  return getFullImageUrl(fileUrl, defaultFile)
}

/**
 * 批量处理图片URL
 * @param imageUrls 图片URL数组
 * @returns 处理后的完整URL数组
 */
export function getFullImageUrls(imageUrls: (string | undefined)[]): string[] {
  return imageUrls.map(url => getFullImageUrl(url))
}

/**
 * 检查是否为完整URL
 * @param url URL字符串
 * @returns 是否为完整URL
 */
export function isFullUrl(url?: string): boolean {
  if (!url) return false
  return url.startsWith('http://') || url.startsWith('https://')
}

/**
 * 图片URL处理的Vue组合式函数
 * 提供响应式的图片URL处理
 */
import { computed, type Ref } from 'vue'

export function useImageUrl(imageUrl: Ref<string | undefined>, defaultImage?: string) {
  const fullImageUrl = computed(() => getFullImageUrl(imageUrl.value, defaultImage))
  
  return {
    fullImageUrl,
    isFullUrl: computed(() => isFullUrl(imageUrl.value))
  }
}

/**
 * 用户头像URL的Vue组合式函数
 */
export function useAvatarUrl(headerImg: Ref<string | undefined>) {
  const avatarUrl = computed(() => getUserAvatarUrl(headerImg.value))

  return {
    avatarUrl,
    isFullUrl: computed(() => isFullUrl(headerImg.value))
  }
}

/**
 * 用户Store头像URL的组合式函数
 * 专门用于处理用户Store中的头像
 */
import { useUserStore } from '@/store/user'

export function useUserStoreAvatar() {
  const userStore = useUserStore()

  const avatarUrl = computed(() => getUserAvatarUrl(userStore.userInfo.headerImg))

  return {
    avatarUrl,
    userInfo: userStore.userInfo,
    isFullUrl: computed(() => isFullUrl(userStore.userInfo.headerImg))
  }
}
