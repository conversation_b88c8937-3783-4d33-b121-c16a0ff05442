import type { IPhoneLoginRequest, IUpdateProfileRequest, IUsernameLoginRequest } from '@/types'
import type { IUser } from '@/types'
import { defineStore } from 'pinia'
import { computed, ref } from 'vue'
import {
  getProfile,
  loginByPhone,
  loginByUsername,
  loginByWechat,
  sendLoginCode,
  updateProfile,
  changePassword,
} from '@/api/auth'

import {
  logout as _logout,
  getWxCode,
} from '@/api/login'
import { toast } from '@/utils/toast'
import { useSocketStore } from './socket'

// 用户状态初始化
const initialUserState: IUser = {
  ID: 0,
  CreatedAt: '',
  UpdatedAt: '',
  uuid: '',
  userName: '',
  phone: '',
  nickName: '',
  headerImg: '/static/images/default-avatar.png',
  companyName: '',
  companyOrgId: '',
  companyAddress: '',
  authorityId: 888,
  enable: 1,
}

export const useUserStore = defineStore(
  'user',
  () => {
    // 统一的用户信息状态
    const userInfo = ref<IUser>({ ...initialUserState })
    const token = ref<string>('')

    // 计算属性：是否已登录
    const isLoggedIn = computed(() => !!token.value && userInfo.value.ID > 0)

    // 设置用户信息
    const setUserInfo = (val: IUser, userToken?: string) => {
      console.log('设置用户信息', val)
      // 若头像为空 则使用默认头像
      if (!val.headerImg) {
        val.headerImg = initialUserState.headerImg
      }
      userInfo.value = val

      if (userToken) {
        token.value = userToken
        uni.setStorageSync('token', userToken)
        // WebSocket认证
        const socketStore = useSocketStore()
        if (socketStore.isConnected) {
          socketStore.authenticate(userToken)
        }
      }

      uni.setStorageSync('userInfo', val)
    }

    const setUserAvatar = (avatar: string) => {
      userInfo.value.headerImg = avatar
      console.log('设置用户头像', avatar)
    }

    // 删除用户信息
    const removeUserInfo = () => {
      userInfo.value = { ...initialUserState }
      token.value = ''
      uni.removeStorageSync('userInfo')
      uni.removeStorageSync('token')
    }

    /**
     * 获取用户资料
     */
    const getUserProfile = async () => {
      const res = await getProfile()
      const userData = res.data.userInfo
      setUserInfo(userData)
      return res
    }

    /**
     * 发送登录验证码
     */
    const sendVerificationCode = async (phone: string) => {
      const res = await sendLoginCode({ phone })
      if (res.code === 0) {
        toast.success(res.msg || '验证码发送成功')
      }
      else {
        toast.error(res.msg || '验证码发送失败')
        throw new Error(res.msg)
      }
      return res
    }

    /**
     * 手机号验证码登录
     */
    const phoneLogin = async (data: IPhoneLoginRequest) => {
      const res = await loginByPhone(data)
      console.log('手机号登录信息', res)
      if (res.code === 0) {
        toast.success(res.msg || '登录成功')
      }
      else {
        toast.error(res.msg || '登录失败')
        throw new Error(res.msg)
      }

      // 设置用户信息和token
      setUserInfo(res.data.user, res.data.token)

      toast.success('登录成功')
      return res
    }


    /**
     * 微信登录（新版）
     */
    const newWxLogin = async () => {
      // 获取微信小程序登录的code
      const data = await getWxCode()
      console.log('微信登录code', data)

      const res = await loginByWechat(data)
      // 设置用户信息和token
      setUserInfo(res.data.user, res.data.token)

      toast.success('微信登录成功')
      return res
    }

    /**
     * 退出登录 并 删除用户信息
     */
    const logout = async () => {
      try {
        await _logout()
      }
      catch (error) {
        console.warn('调用退出登录API失败', error)
      }
      removeUserInfo()
      toast.success('已退出登录')
    }


    /**
     * 用户名密码登录
     */
    const usernameLogin = async (data: IUsernameLoginRequest) => {
      const res = await loginByUsername(data)
      console.log('用户名密码登录信息', res)
      if (res.code === 0) {
        toast.success(res.msg || '登录成功')
      }
      else {
        toast.error(res.msg || '登录失败')
        throw new Error(res.msg)
      }

      // 设置用户信息和token
      setUserInfo(res.data.user, res.data.token)

      toast.success('登录成功')
      return res
    }

    /**
     * 更新用户资料
     */
    const updateUserProfile = async (data: IUpdateProfileRequest) => {
      const res = await updateProfile(data)
      if (res.code === 0) {
        // 更新成功后重新获取用户信息
        await getUserProfile()
        toast.success('资料更新成功')
      }
      else {
        toast.error(res.data.msg || '更新失败')
        throw new Error(res.data.msg)
      }
      return res
    }

    /**
     * 修改密码
     */
    const changeUserPassword = async (data: { oldPassword: string; newPassword: string }) => {
      const res = await changePassword(data)
      if (res.code === 0) {
        toast.success('密码修改成功')
      }
      else {
        toast.error(res.data.msg || '密码修改失败')
        throw new Error(res.data.msg)
      }
      return res
    }

    return {
      // 状态
      userInfo,
      token,
      isLoggedIn,

      // 方法
      phoneLogin,
      usernameLogin,
      sendVerificationCode,
      wxLogin: newWxLogin,
      getUserProfile,
      updateUserProfile,
      changeUserPassword,
      setUserInfo,
      setUserAvatar,
      logout,
      clearUserInfo: removeUserInfo, // 为HTTP错误处理提供清理用户信息的方法
    }
  },
  {
    persist: true,
  },
)
