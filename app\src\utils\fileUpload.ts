import type { IFileUploadResponse } from '@/types/user'

/**
 * 文件上传工具函数
 */
export class FileUploadUtil {
  /**
   * 上传文件到服务器
   * @param filePath 本地文件路径
   * @param token 认证token
   * @param options 上传选项
   */
  static async uploadFile(
    filePath: string,
    token: string,
    options: {
      classId?: string | number
      onProgress?: (progress: number) => void
    } = {}
  ): Promise<IFileUploadResponse['data']['file']> {
    const { classId = '0', onProgress } = options
    const uploadUrl = `${import.meta.env.VITE_SERVER_BASEURL}/fileUploadAndDownload/upload`

    return new Promise((resolve, reject) => {
      const uploadTask = uni.uploadFile({
        url: uploadUrl,
        filePath,
        name: 'file',
        header: {
          'x-token': token,
          'Authorization': `Bearer ${token}`
        },
        formData: {
          classId: String(classId)
        },
        success: (res) => {
          try {
            const responseData: IFileUploadResponse = JSON.parse(res.data)
            
            if (responseData.code === 0 && responseData.data && responseData.data.file) {
              resolve(responseData.data.file)
            } else {
              reject(new Error(responseData.msg || '文件上传失败'))
            }
          } catch (parseError) {
            console.error('响应解析失败:', parseError, '原始响应:', res.data)
            reject(new Error('服务器响应格式错误'))
          }
        },
        fail: (error) => {
          console.error('文件上传请求失败:', error)
          reject(new Error(`上传请求失败: ${error.errMsg || '网络错误'}`))
        }
      })

      // 监听上传进度
      if (onProgress) {
        uploadTask.onProgressUpdate((res) => {
          onProgress(res.progress)
        })
      }
    })
  }

  /**
   * 选择图片文件
   * @param options 选择选项
   */
  static async chooseImage(options: {
    count?: number
    sizeType?: ('original' | 'compressed')[]
    sourceType?: ('album' | 'camera')[]
  } = {}): Promise<string> {
    const {
      count = 1,
      sizeType = ['original', 'compressed'],
      sourceType = ['album', 'camera']
    } = options

    return new Promise((resolve, reject) => {
      uni.chooseImage({
        count,
        sizeType,
        sourceType,
        success: (res) => {
          if (res.tempFilePaths && res.tempFilePaths.length > 0) {
            resolve(res.tempFilePaths[0])
          } else {
            reject(new Error('未选择任何图片'))
          }
        },
        fail: (err) => {
          console.error('选择图片失败:', err)
          reject(new Error('选择图片失败'))
        }
      })
    })
  }

  /**
   * 验证图片文件
   * @param filePath 文件路径
   * @param maxSize 最大文件大小（字节）
   */
  static async validateImage(filePath: string, maxSize: number = 10 * 1024 * 1024): Promise<boolean> {
    return new Promise((resolve) => {
      uni.getFileInfo({
        filePath,
        success: (res) => {
          if (res.size > maxSize) {
            uni.showToast({
              title: `图片大小不能超过${Math.round(maxSize / 1024 / 1024)}MB`,
              icon: 'none'
            })
            resolve(false)
          } else {
            resolve(true)
          }
        },
        fail: () => {
          resolve(true) // 如果无法获取文件信息，则跳过验证
        }
      })
    })
  }
}

/**
 * 头像上传专用函数
 * @param token 用户token
 * @param onProgress 进度回调
 */
export async function uploadAvatar(
  token: string,
  onProgress?: (progress: number) => void
): Promise<string> {
  try {
    // 1. 选择图片
    const filePath = await FileUploadUtil.chooseImage({
      count: 1,
      sizeType: ['compressed'], // 优先使用压缩版本
      sourceType: ['album', 'camera']
    })

    // 2. 验证图片
    const isValid = await FileUploadUtil.validateImage(filePath)
    if (!isValid) {
      throw new Error('图片验证失败')
    }

    // 3. 上传文件
    const fileInfo = await FileUploadUtil.uploadFile(filePath, token, {
      classId: '0',
      onProgress
    })

    return fileInfo.url
  } catch (error: any) {
    console.error('头像上传失败:', error)
    throw error
  }
}
