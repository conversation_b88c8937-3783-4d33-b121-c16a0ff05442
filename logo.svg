<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 主渐变色：品牌蓝紫色渐变 -->
    <linearGradient id="brandGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="rgba(0,0,0,0.12)"/>
    </filter>
  </defs>

  <!-- 圆形背景 -->
  <circle
    cx="60"
    cy="60"
    r="56"
    fill="url(#brandGradient)"
    filter="url(#shadow)"
  />

  <!-- 字母D设计：竖线 + 半圆 -->
  <g fill="white" stroke="none">
    <!-- 竖线 -->
    <rect x="35" y="30" width="6" height="60" rx="3"/>

    <!-- 半圆（右侧弧形） -->
    <path d="M 44 30
             L 68 30
             Q 88 30 88 60
             Q 88 90 68 90
             L 44 90
             Z" />

    <!-- 内部镂空半圆，形成D的内部空间 -->
    <path d="M 50 42
             L 65 42
             Q 76 42 76 60
             Q 76 78 65 78
             L 50 78
             Z"
             fill="url(#brandGradient)"/>
  </g>
</svg>