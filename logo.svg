<?xml version="1.0" encoding="UTF-8"?>
<svg width="120" height="120" viewBox="0 0 120 120" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- 主渐变色：品牌蓝紫色渐变 -->
    <linearGradient id="brandGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#667eea;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#764ba2;stop-opacity:1" />
    </linearGradient>

    <!-- 阴影滤镜 -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="0" dy="4" stdDeviation="8" flood-color="rgba(0,0,0,0.12)"/>
    </filter>
  </defs>

  <!-- 圆形背景 -->
  <circle
    cx="60"
    cy="60"
    r="56"
    fill="url(#brandGradient)"
    filter="url(#shadow)"
  />

  <!-- 字母HD组合设计：H + D 共用中间竖线 -->
  <g fill="white" stroke="none">
    <!-- H的左竖线 -->
    <rect x="35" y="30" width="6" height="60"/>

    <!-- H的横线（连接两个竖线） -->
    <rect x="35" y="57" width="17" height="6"/>

    <!-- H和D共用的中间竖线 -->
    <rect x="46" y="30" width="6" height="60"/>

    <!-- D的右侧半圆弧形 -->
    <path d="M 52 30
             L 76 30
             Q 90 30 90 60
             Q 90 90 76 90
             L 52 90
             Z" />

    <!-- D内部镂空半圆，形成D的内部空间 -->
    <path d="M 58 42
             L 73 42
             Q 78 42 78 60
             Q 78 78 73 78
             L 58 78
             Z"
             fill="url(#brandGradient)"/>
  </g>
</svg>