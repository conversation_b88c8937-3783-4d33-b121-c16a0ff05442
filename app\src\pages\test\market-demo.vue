<template>
  <view class="market-demo">
    <view class="header">
      <text class="title">实时行情演示</text>
      <text class="status" :class="{ connected: socketStore.isConnected }">
        {{ socketStore.isConnected ? '已连接' : '未连接' }}
      </text>
    </view>

    <view class="controls">
      <view class="input-group">
        <input 
          v-model="symbol" 
          placeholder="输入合约代码，如 IF2508"
          class="input"
        />
        <input 
          v-model="exchange" 
          placeholder="输入交易所代码，如 CFFEX"
          class="input"
        />
      </view>
      
      <view class="button-group">
        <button 
          @click="subscribeMarket" 
          :disabled="!socketStore.isConnected || !symbol || !exchange"
          class="btn btn-primary"
        >
          订阅行情
        </button>
        <button 
          @click="unsubscribeMarket" 
          :disabled="!socketStore.isConnected || !symbol"
          class="btn btn-secondary"
        >
          取消订阅
        </button>
      </view>
    </view>

    <view class="subscriptions">
      <text class="section-title">已订阅合约</text>
      <view v-if="marketStore.getSubscribedSymbols().length === 0" class="empty">
        暂无订阅
      </view>
      <view v-else class="subscription-list">
        <view 
          v-for="subscribedSymbol in marketStore.getSubscribedSymbols()" 
          :key="subscribedSymbol"
          class="subscription-item"
        >
          <text class="symbol">{{ subscribedSymbol }}</text>
          <button 
            @click="unsubscribeSymbol(subscribedSymbol)"
            class="btn btn-small btn-danger"
          >
            取消
          </button>
        </view>
      </view>
    </view>

    <view class="market-data">
      <text class="section-title">实时行情数据</text>
      <view v-if="Object.keys(marketStore.getAllMarketData()).length === 0" class="empty">
        暂无行情数据
      </view>
      <view v-else class="data-list">
        <view
          v-for="(data, symbol) in marketStore.getAllMarketData()"
          :key="symbol"
          class="data-item"
        >
          <view class="data-header">
            <text class="symbol">{{ data.symbol }}</text>
            <text class="exchange">{{ data.exchange }}</text>
            <text class="timestamp">{{ formatTime(data.datetime) }}</text>
          </view>
          
          <view class="data-content">
            <view class="price-info">
              <view class="price-item">
                <text class="label">最新价:</text>
                <text class="value price">{{ data.last_price }}</text>
              </view>
              <view class="price-item">
                <text class="label">成交量:</text>
                <text class="value">{{ data.last_volume }}</text>
              </view>
              <view class="price-item">
                <text class="label">成交额:</text>
                <text class="value">{{ data.turnover }}</text>
              </view>
              <view class="price-item">
                <text class="label">持仓量:</text>
                <text class="value">{{ data.open_interest }}</text>
              </view>
            </view>
            
            <view class="quote-info">
              <view class="quote-item">
                <text class="label">买一价:</text>
                <text class="value">{{ data.bid_price_1 }}</text>
                <text class="volume">{{ data.bid_volume_1 }}</text>
              </view>
              <view class="quote-item">
                <text class="label">卖一价:</text>
                <text class="value">{{ data.ask_price_1 }}</text>
                <text class="volume">{{ data.ask_volume_1 }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { useSocketStore, useMarketStore } from '@/store'

const socketStore = useSocketStore()
const marketStore = useMarketStore()

const symbol = ref('rb2511')
const exchange = ref('SHFE')

// 订阅行情
function subscribeMarket() {
  if (!symbol.value || !exchange.value) {
    uni.showToast({
      title: '请输入合约代码和交易所',
      icon: 'none'
    })
    return
  }
  
  marketStore.subscribe(symbol.value.trim(), exchange.value.trim())
}

// 取消订阅
function unsubscribeMarket() {
  if (!symbol.value) {
    uni.showToast({
      title: '请输入合约代码',
      icon: 'none'
    })
    return
  }
  
  marketStore.unsubscribe(symbol.value.trim())
}

// 取消指定合约订阅
function unsubscribeSymbol(symbolToUnsubscribe: string) {
  marketStore.unsubscribe(symbolToUnsubscribe)
}

// 格式化时间
function formatTime(datetime: string): string {
  if (!datetime || datetime === 'None') return '--'
  const date = new Date(datetime)
  return date.toLocaleTimeString()
}

onMounted(() => {
  // 确保WebSocket连接
  if (!socketStore.isConnected) {
    socketStore.connect()
  }
})

onUnmounted(() => {
  // 清理所有订阅和事件处理器
  marketStore.clearAll()
  marketStore.cleanupEventHandlers()
})
</script>

<style scoped>
.market-demo {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: white;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.status {
  padding: 10rpx 20rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  background: #ff4757;
  color: white;
}

.status.connected {
  background: #2ed573;
}

.controls {
  background: white;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.input-group {
  margin-bottom: 20rpx;
}

.input {
  width: 100%;
  padding: 20rpx;
  border: 2rpx solid #ddd;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
  font-size: 28rpx;
}

.button-group {
  display: flex;
  gap: 20rpx;
}

.btn {
  flex: 1;
  padding: 20rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  border: none;
}

.btn-primary {
  background: #3742fa;
  color: white;
}

.btn-secondary {
  background: #747d8c;
  color: white;
}

.btn-danger {
  background: #ff4757;
  color: white;
}

.btn-small {
  padding: 10rpx 20rpx;
  font-size: 24rpx;
}

.btn:disabled {
  background: #ddd;
  color: #999;
}

.subscriptions, .market-data {
  background: white;
  padding: 30rpx;
  border-radius: 10rpx;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.empty {
  text-align: center;
  color: #999;
  padding: 40rpx;
}

.subscription-list {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.subscription-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 10rpx;
}

.data-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.data-item {
  border: 2rpx solid #eee;
  border-radius: 10rpx;
  padding: 20rpx;
}

.data-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 15rpx;
  border-bottom: 2rpx solid #eee;
}

.symbol {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.exchange {
  font-size: 24rpx;
  color: #666;
  background: #e9ecef;
  padding: 5rpx 15rpx;
  border-radius: 15rpx;
}

.timestamp {
  font-size: 24rpx;
  color: #999;
}

.data-content {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.price-info, .quote-info {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.price-item, .quote-item {
  flex: 1;
  min-width: 200rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 15rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
}

.label {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.value {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.value.price {
  color: #ff4757;
}

.volume {
  font-size: 22rpx;
  color: #999;
  margin-top: 5rpx;
}
</style>
