<script lang="ts" setup>
import { ref, computed } from 'vue'
import type { TradeRequestType } from '@/types/trade-request'
import { toast } from '@/utils/toast'

// Props 定义
interface TradeOperationFormProps {
  isPointPrice: boolean
  availableQuantity: number
  weightedPrice: number
  limitUpPrice?: number
  limitDownPrice?: number
  loading: boolean
}

// Emits 定义
const emit = defineEmits<{
  submit: [data: { quantity: number; price?: number }]
  priceClick: [price: number]
}>()

const props = withDefaults(defineProps<TradeOperationFormProps>(), {
  limitUpPrice: undefined,
  limitDownPrice: undefined
})

// 响应式状态
const tradeVolume = ref<number | null>(null)
const requestedPrice = ref<number | null>(null)

// 计算属性
const minQuantity = 1
const maxQuantity = computed(() => props.availableQuantity)

const minPrice = computed(() => props.limitDownPrice)
const maxPrice = computed(() => props.limitUpPrice)

// 按钮禁用状态
const isButtonDisabled = computed(() => {
  // 基本条件：没有输入数量
  if (!tradeVolume.value) return true

  // 没有可用合同
  if (props.availableQuantity === 0) return true

  // 数量超过可用数量
  if (tradeVolume.value > props.availableQuantity) return true

  // 数量小于最小值
  if (tradeVolume.value < minQuantity) return true

  // 点价和洗基差操作都需要输入价格
  if (!requestedPrice.value) return true

  // 检查价格范围
  if (requestedPrice.value) {
    if (minPrice.value && requestedPrice.value < minPrice.value) return true
    if (maxPrice.value && requestedPrice.value > maxPrice.value) return true
  }

  // 正在加载中
  if (props.loading) return true

  return false
})

// 按钮文本
const buttonText = computed(() => {
  if (props.loading) return '提交中...'

  if (props.availableQuantity === 0) {
    return `暂无可用${props.isPointPrice ? '基差' : '固定价'}合同`
  }

  return props.isPointPrice ? '确认点价' : '确认洗基差'
})

// 执行后价格
const executionPrice = computed(() => {
  if (props.isPointPrice) {
    // 点价：基差均价 + 点价价格
    if (!requestedPrice.value) return '--'
    return (props.weightedPrice + requestedPrice.value).toFixed(2)
  } else {
    if (!requestedPrice.value) return '--'
    return (props.weightedPrice - requestedPrice.value).toFixed(2)
  }
})

// 数量输入验证
const quantityInputRules = computed(() => {
  const rules = []
  if (tradeVolume.value) {
    if (tradeVolume.value < minQuantity) {
      rules.push(`数量不能小于 ${minQuantity} 手`)
    }
    if (tradeVolume.value > maxQuantity.value) {
      rules.push(`数量不能大于 ${maxQuantity.value} 手`)
    }
  }
  return rules
})

// 价格输入验证
const priceInputRules = computed(() => {
  const rules = []
  if (requestedPrice.value) {
    if (minPrice.value && requestedPrice.value < minPrice.value) {
      rules.push(`价格不能低于跌停价 ${minPrice.value.toFixed(2)}`)
    }
    if (maxPrice.value && requestedPrice.value > maxPrice.value) {
      rules.push(`价格不能高于涨停价 ${maxPrice.value.toFixed(2)}`)
    }
  }
  return rules
})

// 处理提交
const handleSubmit = () => {
  // 表单验证
  if (!tradeVolume.value || tradeVolume.value <= 0) {
    toast.error('请输入有效的数量')
    return
  }

  if (tradeVolume.value < minQuantity) {
    toast.error(`数量不能小于 ${minQuantity} 手`)
    return
  }

  if (tradeVolume.value > props.availableQuantity) {
    toast.error(`操作数量不能超过可用总量 ${props.availableQuantity} 手`)
    return
  }

  if (props.availableQuantity === 0) {
    toast.error('暂无可用合同，无法进行操作')
    return
  }

  if (!requestedPrice.value || requestedPrice.value <= 0) {
    toast.error('需要输入有效的价格')
    return
  }

  // 价格范围验证
  if (requestedPrice.value) {
    if (minPrice.value && requestedPrice.value < minPrice.value) {
      toast.error(`价格不能低于跌停价 ${minPrice.value.toFixed(2)}`)
      return
    }
    if (maxPrice.value && requestedPrice.value > maxPrice.value) {
      toast.error(`价格不能高于涨停价 ${maxPrice.value.toFixed(2)}`)
      return
    }
  }

  // 发送提交事件
  const submitData = {
    quantity: tradeVolume.value,
    price: requestedPrice.value
  }
  emit('submit', submitData)
}

// 处理行情价格点击
const handlePriceClick = (price: number) => {
  requestedPrice.value = price
  emit('priceClick', price)
}

// 数量微调函数
const increaseQuantity = () => {
  if (!tradeVolume.value) {
    tradeVolume.value = minQuantity
  } else if (tradeVolume.value < maxQuantity.value) {
    tradeVolume.value += 1
  }
}

const decreaseQuantity = () => {
  if (!tradeVolume.value) {
    tradeVolume.value = minQuantity
  } else if (tradeVolume.value > minQuantity) {
    tradeVolume.value -= 1
  }
}

// 价格微调函数
const increasePrice = () => {
  if (!requestedPrice.value) {
    requestedPrice.value = minPrice.value || 0
  } else if (!maxPrice.value || requestedPrice.value < maxPrice.value) {
    requestedPrice.value = Number((requestedPrice.value + 0.5).toFixed(1))
  }
}

const decreasePrice = () => {
  if (!requestedPrice.value) {
    requestedPrice.value = minPrice.value || 0
  } else if (!minPrice.value || requestedPrice.value > minPrice.value) {
    requestedPrice.value = Number((requestedPrice.value - 0.5).toFixed(1))
  }
}

// 重置表单
const resetForm = () => {
  tradeVolume.value = null
  requestedPrice.value = null
}

// 暴露方法给父组件
defineExpose({
  resetForm,
  handlePriceClick
})
</script>

<template>
  <view class="trade-operation-form card bg-white rounded-lg shadow-sm p-4">
    <!-- 数量输入区域 -->
    <view class="horizontal-input-row mb-4">
      <view class="input-label">
        <text class="label-text">操作数量(手)</text>
      </view>
      
      <view class="min-value">
        <text class="range-text">最小</text>
        <text class="range-number">{{ minQuantity }}</text>
      </view>
      
      <view class="input-with-controls flex-1">
        <view class="control-button" @click="decreaseQuantity">
          <text class="control-icon">-</text>
        </view>
        
        <input
          class="number-input"
          type="number"
          placeholder="请输入数量"
          v-model.number="tradeVolume"
          :min="minQuantity"
          :max="maxQuantity"
        />
        
        <view class="control-button" @click="increaseQuantity">
          <text class="control-icon">+</text>
        </view>
      </view>
      
      <view class="max-value">
        <text class="range-text">最大</text>
        <text class="range-number">{{ maxQuantity }}</text>
      </view>
    </view>
    
    <view v-if="quantityInputRules.length > 0" class="error-tips mb-2">
      <text v-for="rule in quantityInputRules" :key="rule" class="text-xs text-red-500 block">
        {{ rule }}
      </text>
    </view>

    <!-- 价格输入区域 -->
    <view class="horizontal-input-row mb-4">
      <view class="input-label">
        <text class="label-text">{{ isPointPrice ? '点价价格' : '洗基差价格' }}</text>
      </view>
      
      <view class="min-value">
        <text class="range-text">跌停</text>
        <text class="range-number">{{ minPrice?.toFixed(0) || '--' }}</text>
      </view>
      
      <view class="input-with-controls flex-1">
        <view class="control-button" @click="decreasePrice">
          <text class="control-icon">-</text>
        </view>
        
        <input
          class="number-input"
          type="number"
          placeholder="请输入点价价格"
          v-model.number="requestedPrice"
          :min="minPrice"
          :max="maxPrice"
          step="0.5"
        />
        
        <view class="control-button" @click="increasePrice">
          <text class="control-icon">+</text>
        </view>
      </view>
      
      <view class="max-value">
        <text class="range-text">涨停</text>
        <text class="range-number">{{ maxPrice?.toFixed(0) || '--' }}</text>
      </view>
    </view>
    
    <view v-if="priceInputRules.length > 0" class="error-tips mb-2">
      <text v-for="rule in priceInputRules" :key="rule" class="text-xs text-red-500 block">
        {{ rule }}
      </text>
    </view>

    <!-- 执行后价格 -->
    <view v-if="requestedPrice" class="estimated-price mb-4">
      <text class="text-sm text-gray-500">{{ isPointPrice ? '执行后价格' : '预估基差价格' }}: </text>
      <text class="text-base font-semibold text-blue-600">{{ executionPrice }}</text>
    </view>

    <!-- 确认按钮 -->
    <wd-button
      :type="isPointPrice ? 'success' : 'primary'"
      block
      :loading="loading"
      :disabled="isButtonDisabled"
      @click="handleSubmit"
    >
      {{ buttonText }}
    </wd-button>
  </view>
</template>

<style lang="scss" scoped>
.trade-operation-form {
  .horizontal-input-row {
    display: flex;
    align-items: center;
    gap: 12rpx;
    height: 80rpx;
    
    .input-label {
      width: 140rpx; // 固定宽度保证对齐
      text-align: right; // 右对齐
      padding-right: 8rpx;
      
      .label-text {
        font-size: 28rpx;
        color: #303133;
        font-weight: 500;
        white-space: nowrap;
      }
    }
    
    .min-value {
      width: 90rpx; // 固定宽度保证对齐
      display: flex;
      flex-direction: column;
      align-items: center; // 居中对齐
      
      .range-text {
        font-size: 20rpx; // 缩小文字
        color: #909399;
        line-height: 1;
        margin-bottom: 4rpx;
      }
      
      .range-number {
        font-size: 22rpx; // 缩小数字
        color: #606266;
        font-weight: 500;
        line-height: 1;
      }
    }
    
    .max-value {
      width: 90rpx; // 固定宽度保证对齐
      display: flex;
      flex-direction: column;
      align-items: flex-start; // 左对齐
      
      .range-text {
        font-size: 20rpx; // 缩小文字
        color: #909399;
        line-height: 1;
        margin-bottom: 4rpx;
      }
      
      .range-number {
        font-size: 22rpx; // 缩小数字
        color: #606266;
        font-weight: 500;
        line-height: 1;
      }
    }
    
    .input-with-controls {
      flex: 1; // 自适应宽度，保证左右对齐
      display: flex;
      align-items: center;
      background-color: #f8f9fa;
      border: 1rpx solid #dcdfe6;
      border-radius: 8rpx;
      overflow: hidden;
      
      .control-button {
        width: 60rpx;
        height: 60rpx;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f0f2f5;
        border-right: 1rpx solid #dcdfe6;
        cursor: pointer;
        transition: background-color 0.3s;
        
        &:last-child {
          border-right: none;
          border-left: 1rpx solid #dcdfe6;
        }
        
        &:active {
          background-color: #e1e4e8;
        }
        
        .control-icon {
          font-size: 32rpx;
          color: #606266;
          font-weight: bold;
          line-height: 1;
        }
      }
      
      .number-input {
        flex: 1;
        height: 60rpx;
        border: none;
        outline: none;
        background: transparent;
        text-align: center;
        font-size: 28rpx;
        color: #303133;
        
        &::placeholder {
          color: #c0c4cc;
          font-size: 26rpx;
        }
        
        &.readonly {
          color: #909399;
          background-color: #f5f7fa;
        }
      }
    }
  }
  
  .error-tips {
    padding-left: 160rpx; // 与label区域对齐（140rpx + 8rpx padding + 12rpx gap）
    
    .text-red-500 {
      color: #f56565;
    }
  }
  
  .estimated-price {
    padding: 12rpx 16rpx;
    background-color: #f7fafc;
    border-radius: 8rpx;
    text-align: center;
  }
}
</style>