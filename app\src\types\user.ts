/**
 * 用户相关类型定义
 * 统一管理所有用户相关的接口和类型
 */

/**
 * 用户基础信息（完整版）- 与Go SysUser模型对齐
 */
export interface IUser {
  // 基础字段
  ID: number
  CreatedAt: string
  UpdatedAt: string
  DeletedAt?: string
  
  // 基本信息  
  uuid: string
  userName: string
  nickName: string
  headerImg: string
  authorityId: number
  enable: number
  
  // 联系方式
  phone: string
  email?: string
  
  // 微信信息
  wechatOpenid?: string
  wechatUnionId?: string
  
  // 企业信息
  companyName?: string
  companyOrgId?: string
  companyAddress?: string
  
  // 关联信息（可选）
  authority?: any
  authorities?: any[]
  originSetting?: Record<string, any>
}

/**
 * 用户信息视图对象（简化版，兼容旧版）
 */
export interface IUserInfoVo {
  id: number
  username: string
  avatar: string
  token: string
}

/**
 * 登录返回的信息
 */
export interface IUserLogin {
  id: string
  username: string
  token: string
}

/**
 * 登录成功响应
 */
export interface ILoginResponse {
  user: IUser
  token: string
  expiresAt: number
}

/**
 * 上传成功的信息
 */
export interface IUploadSuccessInfo {
  fileId: number
  originalName: string
  fileName: string
  storagePath: string
  fileHash: string
  fileType: string
  fileBusinessType: string
  fileSize: number
}

/**
 * 更新用户信息
 */
export interface IUpdateInfo {
  id: number
  name: string
  sex: string
}

/**
 * 更新用户密码
 */
export interface IUpdatePassword {
  id: number
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

/**
 * 更新用户资料请求
 */
export interface IUpdateProfileRequest {
  nickName?: string
  headerImg?: string
  companyName?: string
  companyOrgId?: string
  companyAddress?: string
}

/**
 * 文件上传响应
 */
export interface IFileUploadResponse {
  code: number
  msg: string
  data: {
    file: {
      ID: number
      CreatedAt: string
      UpdatedAt: string
      name: string
      classId: number
      url: string
      tag: string
      key: string
    }
  }
}

/**
 * 可选择的用户信息（精简版，用于用户选择器）
 * 仅包含必要的展示信息，保护用户隐私
 */
export interface IUserSelectable {
  ID: number
  nickName: string
  phone: string
}