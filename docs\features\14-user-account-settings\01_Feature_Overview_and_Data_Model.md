# 功能模块：用户账户设置

## 1. 功能模块简介 (Summary)

本模块为登录用户提供了一个集中管理个人账户信息的平台。其核心目标是允许用户安全、便捷地查看和修改自己的个人及企业资料，并能独立完成密码的修改操作，从而增强账户的自主性和安全性。

- **资料管理**: 用户可以查看并编辑自己的昵称、头像，以及关联的企业名称、地址等信息。
- **安全设置**: 用户可以通过“旧密码 + 新密码”的方式修改自己的登录密码，提升账户安全等级。

## 2. 数据定义 (Data Definition)

本功能模块不涉及新增数据库表，主要复用并扩展现有的 `sys_users` 表。

### 2.1. `sys_users` - 用户表 (复用)

本功能直接与 `sys_users` 表交互，以下是涉及的主要字段：

| 字段名 | 类型 | 约束 | 描述 |
| :--- | :--- | :--- | :--- |
| `id` | `BIGINT` | `PK` | 用户唯一ID |
| `uuid` | `uuid.UUID` | `Unique` | 用户业务UUID |
| `password` | `VARCHAR` | `NOT NULL` | 用户登录密码（加密存储） |
| `nick_name` | `VARCHAR` | | 用户昵称 |
| `header_img` | `VARCHAR` | | 用户头像URL |
| `company_name` | `VARCHAR` | | 企业名称 |
| `company_org_id` | `VARCHAR` | | 企业组织编码ID |
| `company_address`| `VARCHAR` | | 企业地址 |
