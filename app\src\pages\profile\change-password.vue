<route lang="json">
{
  "style": {
    "navigationBarTitleText": "修改密码"
  }
}
</route>

<template>
  <view class="change-password-container">
    <!-- 页面标题 -->
    <view class="page-header">
      <view class="header-icon">
        <wd-icon name="lock-on" size="48rpx" color="#667eea" />
      </view>
      <text class="header-title">修改密码</text>
      <text class="header-subtitle">为了您的账户安全，请定期修改密码</text>
    </view>

    <!-- 密码表单 -->
    <view class="password-form">
      <!-- 旧密码 -->
      <view class="form-item">
        <wd-input
          v-model="formData.oldPassword"
          placeholder="请输入当前密码"
          clearable
          :show-password="true"
          custom-class="password-input"
        />
      </view>

      <!-- 新密码 -->
      <view class="form-item">
        <wd-input
          v-model="formData.newPassword"
          placeholder="请输入新密码"
          clearable
          :show-password="true"
          custom-class="password-input"
        />
        <view class="password-tips">
          <text>密码长度至少6位，建议包含字母、数字和特殊字符</text>
        </view>
      </view>

      <!-- 确认新密码 -->
      <view class="form-item">
        <wd-input
          v-model="formData.confirmPassword"
          placeholder="请再次输入新密码"
          clearable
          :show-password="true"
          custom-class="password-input"
        />
        <view v-if="showPasswordMismatchError" class="error-tips">
          <text>两次输入的密码不一致</text>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section">
      <wd-button 
        type="primary" 
        size="large" 
        custom-class="submit-btn"
        @click="handleSubmit"
        :loading="submitting"
        :disabled="!isFormValid"
      >
        {{ submitting ? '修改中...' : '确认修改' }}
      </wd-button>
    </view>

    <!-- 安全提示 -->
    <view class="security-tips">
      <view class="tips-header">
        <wd-icon name="info" size="32rpx" color="#f56c6c" />
        <text>安全提示</text>
      </view>
      <view class="tips-content">
        <text>• 密码修改成功后，您需要重新登录</text>
        <text>• 请妥善保管您的新密码，不要泄露给他人</text>
        <text>• 建议定期更换密码以保障账户安全</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useUserStore } from '@/store/user'
import { changePassword } from '@/api/auth'
import { toast } from '@/utils/toast'

const userStore = useUserStore()

// 表单数据
const formData = ref({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 提交状态
const submitting = ref(false)

// 表单验证
const isFormValid = computed(() => {
  const { oldPassword, newPassword, confirmPassword } = formData.value
  return oldPassword.length >= 6 &&
         newPassword.length >= 6 &&
         confirmPassword.length >= 6 &&
         newPassword === confirmPassword
})

// 密码不匹配错误提示
const showPasswordMismatchError = computed(() => {
  const { newPassword, confirmPassword } = formData.value
  return confirmPassword.length > 0 && newPassword.length > 0 && newPassword !== confirmPassword
})

// 密码强度验证
const validatePassword = (password: string) => {
  if (password.length < 6) {
    return '密码长度至少6位'
  }
  return ''
}

// 提交表单
const handleSubmit = async () => {
  const { oldPassword, newPassword, confirmPassword } = formData.value

  // 基础验证
  if (!oldPassword) {
    toast.error('请输入当前密码')
    return
  }

  if (!newPassword) {
    toast.error('请输入新密码')
    return
  }

  if (!confirmPassword) {
    toast.error('请确认新密码')
    return
  }

  // 密码长度验证
  const passwordError = validatePassword(newPassword)
  if (passwordError) {
    toast.error(passwordError)
    return
  }

  // 新密码确认验证
  if (newPassword !== confirmPassword) {
    toast.error('两次输入的新密码不一致')
    return
  }

  // 新旧密码不能相同
  if (oldPassword === newPassword) {
    toast.error('新密码不能与当前密码相同')
    return
  }

  // 提交修改
  submitting.value = true
  try {
    const response = await changePassword({
      oldPassword,
      newPassword
    })

    if (response.code === 0) {
      toast.success('密码修改成功，请重新登录')
      
      // 清除用户信息并跳转到登录页
      setTimeout(async () => {
        await userStore.logout()
        uni.reLaunch({
          url: '/pages/login/index'
        })
      }, 1500)
    } else {
      toast.error(response.data.msg || '密码修改失败')
    }
  } catch (error: any) {
    console.error('修改密码失败:', error)
    
    // 处理具体的错误信息
    if (error.data?.msg) {
      toast.error(error.data.msg)
    } else if (error.errMsg) {
      toast.error(error.errMsg)
    } else {
      toast.error('密码修改失败，请重试')
    }
  } finally {
    submitting.value = false
  }
}
</script>

<style lang="scss" scoped>
.change-password-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 40rpx 32rpx;
}

.page-header {
  text-align: center;
  margin-bottom: 60rpx;
  
  .header-icon {
    width: 120rpx;
    height: 120rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 60rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 32rpx;
    box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
  }
  
  .header-title {
    display: block;
    font-size: 40rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
  }
  
  .header-subtitle {
    font-size: 26rpx;
    color: #909399;
    line-height: 1.5;
  }
}

.password-form {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 40rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(10rpx);
}

.form-item {
  margin-bottom: 24rpx;

  &:last-child {
    margin-bottom: 0;
  }
}

:deep(.password-input) {
  border: 2rpx solid #e4e7ed !important;
  border-radius: 12rpx !important;
  height: 88rpx !important;
  font-size: 28rpx !important;
  padding: 0 24rpx !important;

  &:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1) !important;
  }
}

.password-tips {
  margin-top: 12rpx;

  text {
    font-size: 24rpx;
    color: #909399;
    line-height: 1.4;
  }
}

.error-tips {
  margin-top: 12rpx;

  text {
    font-size: 24rpx;
    color: #f56c6c;
    line-height: 1.4;
  }
}

.submit-section {
  margin-bottom: 60rpx;
}

:deep(.submit-btn) {
  width: 100% !important;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
  border: none !important;
  border-radius: 44rpx !important;
  height: 88rpx !important;
  font-size: 32rpx !important;
  font-weight: 600 !important;

  &[disabled] {
    background: #c0c4cc !important;
    opacity: 0.6 !important;
  }
}

.security-tips {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20rpx;
  padding: 32rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  backdrop-filter: blur(10rpx);
}

.tips-header {
  display: flex;
  align-items: center;
  margin-bottom: 24rpx;
  
  text {
    margin-left: 16rpx;
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
  }
}

.tips-content {
  text {
    display: block;
    font-size: 26rpx;
    color: #606266;
    line-height: 2;
    margin-bottom: 8rpx;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
}
</style>
