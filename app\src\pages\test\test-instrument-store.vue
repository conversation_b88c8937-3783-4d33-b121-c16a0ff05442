<route lang="jsonc" type="page">
{
  "style": {
    "navigationBarTitleText": "测试Instrument Store"
  }
}
</route>

<script lang="ts" setup>
import { ref, computed, onMounted } from 'vue'
import { useInstrumentStore } from '@/store/instrument'

defineOptions({
  name: 'TestInstrumentStore'
})

const instrumentStore = useInstrumentStore()
const testResults = ref<string[]>([])
const isRunning = ref(false)

// 添加测试结果
function addResult(message: string, isError = false) {
  const timestamp = new Date().toLocaleTimeString()
  const prefix = isError ? '❌' : '✅'
  testResults.value.push(`${prefix} [${timestamp}] ${message}`)
}

// 清空测试结果
function clearResults() {
  testResults.value = []
}

// 运行所有测试
async function runAllTests() {
  if (isRunning.value) return
  
  isRunning.value = true
  clearResults()
  
  try {
    addResult('开始测试 Instrument Store...')
    
    // 测试1: 加载数据
    addResult('测试1: 加载合约数据')
    const loadResult = await instrumentStore.loadInstruments(true) // 强制刷新
    if (loadResult.success) {
      addResult(`数据加载成功，共 ${instrumentStore.instruments.length} 个合约`)
    } else {
      addResult(`数据加载失败: ${loadResult.error}`, true)
      return
    }
    
    // 测试2: 获取交易所列表
    addResult('测试2: 获取交易所列表')
    const exchanges = instrumentStore.getExchanges()
    addResult(`获取到 ${exchanges.length} 个交易所: ${exchanges.map(e => e.label).join(', ')}`)
    
    // 测试3: 按ID获取合约
    addResult('测试3: 按ID获取合约')
    if (instrumentStore.instruments.length > 0) {
      const firstInstrument = instrumentStore.instruments[0]
      const foundById = instrumentStore.getInstrumentById(firstInstrument.id)
      if (foundById && foundById.id === firstInstrument.id) {
        addResult(`按ID获取成功: ${foundById.instrument_id}`)
      } else {
        addResult('按ID获取失败', true)
      }
    }
    
    // 测试4: 按instrument_id获取合约
    addResult('测试4: 按instrument_id获取合约')
    if (instrumentStore.instruments.length > 0) {
      const firstInstrument = instrumentStore.instruments[0]
      const foundByInstrumentId = instrumentStore.getInstrumentByInstrumentId(firstInstrument.instrument_id)
      if (foundByInstrumentId && foundByInstrumentId.instrument_id === firstInstrument.instrument_id) {
        addResult(`按instrument_id获取成功: ${foundByInstrumentId.instrument_id}`)
      } else {
        addResult('按instrument_id获取失败', true)
      }
    }
    
    // 测试5: 获取产品列表
    addResult('测试5: 获取产品列表')
    if (exchanges.length > 0) {
      const firstExchange = exchanges[0].value
      const products = instrumentStore.getProductsByExchange(firstExchange)
      addResult(`交易所 ${firstExchange} 有 ${products.length} 个产品`)
    }
    
    // 测试6: 获取合约列表
    addResult('测试6: 获取合约列表')
    if (exchanges.length > 0 && instrumentStore.instruments.length > 0) {
      const firstInstrument = instrumentStore.instruments[0]
      const contracts = instrumentStore.getContractsByProduct(firstInstrument.exchange_id, firstInstrument.product_name)
      addResult(`产品 ${firstInstrument.product_name} 有 ${contracts.length} 个合约`)
    }
    
    // 测试7: 搜索功能
    addResult('测试7: 搜索功能')
    const searchResults = instrumentStore.searchInstruments('i25')
    addResult(`搜索 'i25' 找到 ${searchResults.length} 个结果`)
    
    // 测试8: 安全获取方法
    addResult('测试8: 安全获取方法')
    if (instrumentStore.instruments.length > 0) {
      const firstInstrument = instrumentStore.instruments[0]
      const safeResult = await instrumentStore.safeGetInstrumentById(firstInstrument.id)
      if (safeResult) {
        addResult(`安全获取成功: ${safeResult.instrument_id}`)
      } else {
        addResult('安全获取失败', true)
      }
    }
    
    addResult('所有测试完成！')
    
  } catch (error) {
    addResult(`测试过程中出现错误: ${error}`, true)
  } finally {
    isRunning.value = false
  }
}

// 测试缓存功能
async function testCache() {
  if (isRunning.value) return
  
  isRunning.value = true
  addResult('测试缓存功能...')
  
  try {
    // 第一次加载
    const start1 = Date.now()
    await instrumentStore.loadInstruments(true) // 强制刷新
    const time1 = Date.now() - start1
    addResult(`首次加载耗时: ${time1}ms`)
    
    // 第二次加载（应该使用缓存）
    const start2 = Date.now()
    await instrumentStore.loadInstruments()
    const time2 = Date.now() - start2
    addResult(`缓存加载耗时: ${time2}ms`)
    
    if (time2 < time1 / 2) {
      addResult('缓存功能正常工作')
    } else {
      addResult('缓存功能可能有问题', true)
    }
    
  } catch (error) {
    addResult(`缓存测试失败: ${error}`, true)
  } finally {
    isRunning.value = false
  }
}

// 显示store状态
const storeStatus = computed(() => {
  return {
    isLoaded: instrumentStore.isLoaded,
    isLoading: instrumentStore.isLoading,
    isDataFresh: instrumentStore.isDataFresh,
    instrumentCount: instrumentStore.instruments.length,
    error: instrumentStore.error
  }
})

onMounted(() => {
  addResult('页面已加载，可以开始测试')
})
</script>

<template>
  <view class="test-page">
    <view class="header">
      <text class="title">Instrument Store 测试</text>
    </view>
    
    <!-- Store状态 -->
    <view class="status-section">
      <text class="section-title">Store状态</text>
      <view class="status-item">
        <text>已加载: {{ storeStatus.isLoaded ? '是' : '否' }}</text>
      </view>
      <view class="status-item">
        <text>加载中: {{ storeStatus.isLoading ? '是' : '否' }}</text>
      </view>
      <view class="status-item">
        <text>数据新鲜: {{ storeStatus.isDataFresh ? '是' : '否' }}</text>
      </view>
      <view class="status-item">
        <text>合约数量: {{ storeStatus.instrumentCount }}</text>
      </view>
      <view v-if="storeStatus.error" class="status-item error">
        <text>错误: {{ storeStatus.error }}</text>
      </view>
    </view>
    
    <!-- 测试按钮 -->
    <view class="button-section">
      <wd-button 
        type="primary" 
        :disabled="isRunning"
        @click="runAllTests"
        custom-class="test-button"
      >
        {{ isRunning ? '测试中...' : '运行所有测试' }}
      </wd-button>
      
      <wd-button 
        type="success" 
        :disabled="isRunning"
        @click="testCache"
        custom-class="test-button"
      >
        {{ isRunning ? '测试中...' : '测试缓存功能' }}
      </wd-button>
      
      <wd-button 
        type="warning" 
        @click="clearResults"
        custom-class="test-button"
      >
        清空结果
      </wd-button>
    </view>
    
    <!-- 测试结果 -->
    <view class="results-section">
      <text class="section-title">测试结果</text>
      <view class="results-container">
        <view 
          v-for="(result, index) in testResults" 
          :key="index" 
          class="result-item"
          :class="{ 'error': result.includes('❌') }"
        >
          <text class="result-text">{{ result }}</text>
        </view>
        <view v-if="testResults.length === 0" class="empty-results">
          <text>暂无测试结果</text>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.test-page {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 30rpx;
  
  .title {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
  }
}

.status-section, .results-section {
  background: white;
  border-radius: 12rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
  
  .section-title {
    font-size: 32rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
    display: block;
  }
}

.status-item {
  padding: 10rpx 0;
  border-bottom: 1rpx solid #eee;
  
  &:last-child {
    border-bottom: none;
  }
  
  &.error {
    color: #ff4757;
  }
  
  text {
    font-size: 28rpx;
  }
}

.button-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-bottom: 30rpx;
  
  .test-button {
    width: 100%;
  }
}

.results-container {
  max-height: 800rpx;
  overflow-y: auto;
}

.result-item {
  padding: 15rpx;
  margin-bottom: 10rpx;
  background: #f8f9fa;
  border-radius: 8rpx;
  border-left: 4rpx solid #28a745;
  
  &.error {
    border-left-color: #dc3545;
    background: #fff5f5;
  }
  
  .result-text {
    font-size: 26rpx;
    line-height: 1.4;
    word-break: break-all;
  }
}

.empty-results {
  text-align: center;
  padding: 40rpx;
  color: #999;
  
  text {
    font-size: 28rpx;
  }
}
</style>
