# 02 - 个人资料与密码管理

---

## 1. 功能设计 (Functional Design)

### 1.1. 账户中心页面

- **目标**: 提供一个统一的入口页面，展示用户的核心信息，并作为各项设置功能的导航。
- **业务流程**:
  1. 用户进入“我的”或“账户中心”页面。
  2. 页面加载时，自动调用后端接口获取当前登录用户的完整个人及企业资料。
  3. 资料以列表或卡片形式清晰展示，关键信息（如头像、昵称）支持点击后直接进入编辑状态。
  4. 页面提供明确的入口，如“修改密码”、“合作伙伴管理”等，引导用户进行下一步操作。

### 1.2. 编辑个人/企业资料

- **目标**: 允许用户修改其个人昵称、头像以及企业相关信息。
- **业务流程**:
  1. 用户在账户中心页面点击“编辑资料”或相应的字段。
  2. 进入编辑模式或弹出编辑表单，表单中预先填充用户当前的数据。
  3. 用户修改信息后，点击“保存”。
  4. 前端将修改后的数据提交至后端 `/user/updateProfile` 接口 (已存在)。
  5. 后端验证通过后更新数据库，并返回成功信息。前端刷新页面以显示最新资料。

### 1.3. 修改密码

- **目标**: 提供一个安全的密码修改通道。
- **业务流程**:
  1. 用户在账户中心页面点击“修改密码”，进入独立的密码修改页面。
  2. 页面要求用户输入 **旧密码**、**新密码** 和 **确认新密码**。
  3. 前端进行基础校验（如新密码与确认密码是否一致，密码强度等）。
  4. 用户提交后，前端将数据发送至后端 `/user/changePassword` 接口 (已存在)。
  5. 后端首先验证旧密码是否正确，然后更新为新密码。
  6. 修改成功后，建议强制用户重新登录以确保安全。

---

## 2. 数据模型 (Data Model)

### 2.1. 用户模型 (SysUser)

`SysUser` 是核心的用户数据模型，用于在前后端之间传递用户信息。

| 字段 (Field) | 类型 (Type) | 说明 (Description) | 备注 (Notes) |
| :--- | :--- | :--- | :--- |
| `uuid` | `string` | 用户唯一标识符 | |
| `userName` | `string` | 用户登录名 | 不可修改 |
| `nickName` | `string` | 用户昵称 | **可修改** |
| `headerImg` | `string` | 用户头像URL | **可修改** |
| `mobile` | `string` | 手机号码 | |
| `email` | `string` | 电子邮箱 | |
| `companyName` | `string` | 企业名称 | **可修改** |
| `companyId` | `uint` | 企业ID | |
| `authorityId` | `uint` | 角色ID | |
| `authorities` | `[]Authority` | 角色信息 | |

---

## 3. 接口定义 (API Definition)

### 3.1 主要接口 (已存在，可直接复用)

| 功能描述 | HTTP 方法 | 路径 (Endpoint) | 请求体说明 | 响应体说明 | 状态 |
| :--- | :--- | :--- | :--- | :--- | :--- |
| **获取当前用户信息** | `GET` | `/user/getProfile` | (无) - 通过JWT获取用户 | `{"code": 0, "data": {<SysUser>}}` | ✅ **已存在** |
| **更新当前用户信息** | `PUT` | `/user/updateProfile` | `{"nickName": "...", "headerImg": "...", "companyName": "..."}` | `{"code": 0, "msg": "更新成功"}` | ✅ **已存在** |
| **修改密码** | `POST` | `/user/changePassword` | `{"oldPassword": "...", "newPassword": "..."}` | `{"code": 0, "msg": "密码修改成功"}` | ✅ **已存在** |

### 3.2 备选接口 (系统管理模块)

| 功能描述 | HTTP 方法 | 路径 (Endpoint) | 说明 | 状态 |
| :--- | :--- | :--- | :--- | :--- |
| **获取用户信息** | `GET` | `/user/getUserInfo` | 系统管理模块的用户信息获取接口 | ✅ **已存在** |
| **设置自身信息** | `PUT` | `/user/setSelfInfo` | 系统管理模块的用户信息更新接口 | ✅ **已存在** |

### 3.3 头像上传接口方案

| 方案 | 接口路径 | 状态 | 推荐度 | 说明 |
| :--- | :--- | :--- | :--- | :--- |
| **方案一：复用现有接口** | `POST /fileUploadAndDownload/upload` | ✅ **已存在** | ⭐ **推荐** | 功能完善，支持多种云存储 |
| **方案二：新增专门接口** | `POST /user/avatar` | ❌ **需开发** | 可选 | 语义清晰，需额外开发 |

#### 方案一：复用现有文件上传接口 ⭐ **推荐**

**实施流程**：
1. **上传头像**: `POST /fileUploadAndDownload/upload` → 获取文件URL
2. **更新资料**: `PUT /user/updateProfile` → 更新 `headerImg` 字段

**优势**：
- ✅ 无需开发新接口，开发效率高
- ✅ 支持多种云存储（阿里云OSS、华为OBS、AWS S3等）
- ✅ 已有完整的前端组件支持（UploadImage、CropperImage等）
- ✅ 支持图片裁剪功能


### 3.4 接口复用说明

**推荐使用的接口组合**:
- 获取用户信息: 使用 `GET /user/getProfile` (来自 dianjia/auth.go)
- 更新用户信息: 使用 `PUT /user/updateProfile` (来自 dianjia/auth.go)
- 修改密码: 使用 `POST /user/changePassword` (来自 system/sys_user.go)
- 头像上传: 使用 `POST /fileUploadAndDownload/upload` (来自 example/exa_file_upload_and_download.go) ⭐ **推荐**

**接口位置**:
- `admin/server/router/dianjia/auth.go` - 包含用户资料相关接口
- `admin/server/router/system/sys_user.go` - 包含密码管理和系统用户管理接口
- `admin/server/router/example/exa_file_upload_and_download.go` - 包含文件上传接口

**注意事项**
dianjia/auth.go 是可以根据情况修改的问题。 
system/sys_user.go 尽量不修改。 

---

## 4. 前端组件设计 (Frontend Component Design)

### 4.1. 账户中心主页 (`profile/index.vue`)

- **页面结构**:
  - 使用 `uni-list` 或类似组件展示用户信息。
  - 列表项包括：头像、昵称、企业名称、登录账号、手机号等。
- **核心组件**:
  - **头像展示与修改**:
    - 点击头像区域，调用 `uni.chooseImage` 选择图片。
    - 使用内置的 `CropperImage` 组件进行图片裁剪。
    - 上传裁剪后的图片至 `/fileUploadAndDownload/upload`。
    - 获取返回的 URL 后，与其他资料一同通过 `/user/updateProfile` 保存。
  - **资料编辑**:
    - 昵称、企业名称等字段提供 "编辑" 功能，点击后切换为输入框。
    - 提供 "保存" 和 "取消" 按钮。
    - "保存" 按钮调用 `/user/updateProfile` 接口。
- **导航功能**:
  - 包含一个明显的 "修改密码" 入口，点击后跳转至 `change-password.vue` 页面。

### 4.2. 修改密码页 (`profile/change-password.vue`)

- **页面结构**:
  - 一个表单，包含三个输入字段：
    - 旧密码 (`oldPassword`)
    - 新密码 (`newPassword`)
    - 确认新密码 (`confirmPassword`)
  - 所有输入框类型均为 `password`。
- **核心逻辑**:
  - **表单校验**:
    - 提交前进行客户端校验：
      - 所有字段不能为空。
      - 新密码与确认新密码必须一致。
      - (可选) 新密码应满足一定的复杂度要求（如长度、字符类型等）。
  - **接口调用**:
    - 校验通过后，调用 `POST /user/changePassword` 接口。
    - 请求体为 `{"oldPassword": "...", "newPassword": "..."}`。
- **交互反馈**:
  - **成功**: 显示成功提示（如 "密码修改成功，请重新登录"），然后清除本地用户凭证，并跳转到登录页。
  - **失败**: 根据后端返回的错误信息（如 "旧密码错误"）给出相应提示。

---

## 5. 相关页面 (Related Pages)

- **账户中心主页**: `app/src/pages/profile/index.vue`
- **修改密码页**: `app/src/pages/profile/change-password.vue`

---

## 6. 测试用例 (Test Cases)

| 用例ID | 场景描述 | 测试步骤 | 预期结果 |
| :--- | :--- | :--- | :--- |
| `TC-ACC-001` | **成功查看用户资料** | 1. 用户登录后进入“账户中心”。 | 页面正确显示用户的昵称、企业等信息。 |
| `TC-ACC-002` | **成功更新用户资料** | 1. 进入“账户中心”。 2. 修改昵称为“新昵称”并保存。 | 提示“更新成功”，刷新后页面显示“新昵称”。 |
| `TC-ACC-003` | **成功修改密码** | 1. 进入“修改密码”页。 2. 输入正确的旧密码、新密码和确认密码。 3. 点击“确认修改”。 | 提示“修改成功”，用户被要求重新登录。 |
| `TC-ACC-004` | **修改密码失败 - 旧密码错误** | 1. 进入“修改密码”页。 2. 输入错误的旧密码。 3. 点击“确认修改”。 | 提示“旧密码不正确”，停留在当前页面。 |
| `TC-ACC-005` | **修改密码失败 - 新密码不一致** | 1. 进入“修改密码”页。 2. 输入的新密码与确认密码不一致。 3. 点击“确认修改”。 | 前端提示“两次输入的新密码不一致”。 |

---

## 7. 实施建议 (Implementation Recommendations)

### 7.1 接口复用优势
- ✅ **开发效率**: 所有核心接口已存在，无需重新开发
- ✅ **一致性**: 使用现有接口保证了系统的一致性
- ✅ **稳定性**: 现有接口已经过测试，降低了风险

### 7.2 前端开发重点
- **重点关注**: 前端页面开发和用户体验优化
- **接口对接**: 直接使用现有接口，注意请求格式和响应处理
- **路由配置**: 确保前端路由与后端接口路径匹配

---

## 8. 注意事项 (Notes/Caveats)

- **安全性**: 修改密码接口必须严格验证用户身份和旧密码，并对传输过程进行加密。建议增加请求频率限制，防止暴力破解。
- **用户体验**: 密码修改成功后，应清晰地告知用户需要重新登录，并自动导航至登录页面。
- **数据校验**: 前后端都需要对用户输入的数据（特别是密码强度）进行校验。
- **接口兼容性**: 在使用现有接口时，需要确认请求参数和响应格式是否符合前端需求。

---

## 9. 总结 (Summary)

本方案旨在实现用户个人资料与密码管理功能。核心策略是 **最大化复用现有接口**，将开发重点集中在 **前端页面实现和用户体验优化** 上。

- **后端**: 无需开发新接口。仅需确保 `dianjia/auth.go`、`system/sys_user.go` 和 `example/exa_file_upload_and_download.go` 中的相关接口稳定可用。
- **前端**: 主要工作是开发 `账户中心` 和 `修改密码` 两个页面，并与现有接口进行对接。需重点处理好图片上传裁剪、表单校验和用户交互反馈。

通过此方案，可以高效、稳定地完成该功能的开发，并确保与现有系统功能的一致性。