<route lang="jsonc" type="home">
{
  "layout": "default",
  "style": {
    "navigationStyle": "custom",
    "navigationBarTitleText": "工作台"
  }
}
</route>

<script lang="ts" setup>
import { ref } from 'vue'
import { useUserStore } from '@/store/user'

defineOptions({
  name: 'WorkspaceIndex',
})

const userStore = useUserStore()

// 更新文案和图标名称
const sellerMenu = ref([
  {
    title: '合同管理',
    icon: 'file-word',
    path: '/pages/contract/setter-list',
    color: 'text-blue-500',
  },
  {
    title: '交易审核',
    icon: 'check-circle',
    path: '/pages/trade/setter-management',
    color: 'text-green-500',
  },
  {
    title: '我的报价',
    icon: 'read',
    path: '/pages/quotes/my-list',
    color: 'text-purple-500',
  },
  {
    title: '报表', // Changed
    icon: 'chart-pie',
    path: '/pages/reports/setter-view',
    color: 'text-orange-500',
  },
])

const pricerMenu = ref([
  {
    title: '合同中心',
    icon: 'cart',
    path: '/pages/contract/pricer-list',
    color: 'text-purple-500',
  },
  {
    title: '发起点价',
    icon: 'add-circle',
    path: '/pages/trade/execute',
    color: 'text-red-500',
  },
  {
    title: '我的交易',
    icon: 'list',
    path: '/pages/trade/pricer-management',
    color: 'text-indigo-500',
  },
  {
    title: '报表', // Changed
    icon: 'chart-pie',
    path: '/pages/reports/pricer-view',
    color: 'text-amber-500',
  },
])

const commonMenu = ref([
  {
    title: '行情看板',
    icon: 'chart-bar',
    path: '/pages/dashboard/index',
    color: 'text-cyan-500',
  },
  {
    title: '报价市场',
    icon: 'shop',
    path: '/pages/quotes/marketplace',
    color: 'text-green-500',
  },
  {
    title: '消息通知',
    icon: 'notification',
    path: '/pages/notifications/index',
    color: 'text-amber-500',
  },
  {
    title: '账户中心',
    icon: 'setting',
    path: '/pages/profile/index',
    color: 'text-gray-500',
  },
])

function handleNavigate(path: string) {
  if (!path) return
  uni.navigateTo({ url: path })
}
</script>

<template>
  <view class="workspace-page min-h-screen bg-gray-100">
    <wd-navbar title="工作台" :fixed="false" />

    <view class="p-4">
      <!-- 用户欢迎语 -->
      <view class="user-welcome mb-4">
        <h1 class="text-xl font-bold text-gray-800">您好, {{ userStore.userInfo.nickName }}</h1>
        <p class="text-sm text-gray-500 mt-1">欢迎使用大宗商品交易风险管理平台</p>
      </view>

      <!-- 被点价方工作台 -->
      <view class="bg-white rounded-lg shadow-sm mb-4 overflow-hidden">
        <view class="p-3">
          <h2 class="text-base font-semibold text-gray-800">被点价方工作台</h2>
        </view>
        <view class="grid grid-cols-4 divide-y divide-x divide-gray-100">
          <view
            v-for="item in sellerMenu"
            :key="item.title"
            class="menu-item flex flex-col items-center justify-center p-4 transition-all duration-200 active:bg-gray-100"
            @click="handleNavigate(item.path)"
          >
            <wd-icon :name="item.icon" :custom-class="item.color" size="30px"></wd-icon>
            <text class="mt-2 text-xs text-gray-700">{{ item.title }}</text>
          </view>
        </view>
      </view>

      <!-- 点价方工作台 -->
      <view class="bg-white rounded-lg shadow-sm mb-4 overflow-hidden">
        <view class="p-3">
          <h2 class="text-base font-semibold text-gray-800">点价方工作台</h2>
        </view>
        <view class="grid grid-cols-4 divide-y divide-x divide-gray-100">
          <view
            v-for="item in pricerMenu"
            :key="item.title"
            class="menu-item flex flex-col items-center justify-center p-4 transition-all duration-200 active:bg-gray-100"
            @click="handleNavigate(item.path)"
          >
            <wd-icon :name="item.icon" :custom-class="item.color" size="30px"></wd-icon>
            <text class="mt-2 text-xs text-gray-700">{{ item.title }}</text>
          </view>
        </view>
      </view>

      <!-- 通用工具区 -->
      <view class="bg-white rounded-lg shadow-sm overflow-hidden">
        <view class="p-3">
          <h2 class="text-base font-semibold text-gray-800">通用工具</h2>
        </view>
        <view class="grid grid-cols-4 divide-y divide-x divide-gray-100">
          <view
            v-for="item in commonMenu"
            :key="item.title"
            class="menu-item flex flex-col items-center justify-center p-4 transition-all duration-200 active:bg-gray-100"
            @click="handleNavigate(item.path)"
          >
            <wd-icon :name="item.icon" :custom-class="item.color" size="30px"></wd-icon>
            <text class="mt-2 text-xs text-gray-700">{{ item.title }}</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</template>

<style lang="scss" scoped>
.workspace-page {
  background-color: #f7f8fa;
}
</style>