# 03 - 应用支持与设置

---

## 1. 功能设计 (Functional Design)

本部分功能旨在提升用户体验，并为用户提供必要的支持和法律信息渠道。

### 1.1. 清理缓存

- **目标**: 允许用户手动清除应用的本地缓存数据，以解决潜在的数据不同步或显示异常问题，并释放存储空间。
- **业务流程**:
  1. 用户在“账户中心”或“设置”页面找到“清理缓存”按钮。
  2. 点击后，系统弹窗进行二次确认：“确定要清理缓存吗？部分数据可能需要重新加载。”
  3. 用户确认后，前端执行 `uni.clearStorageSync()` 或其他选择性的缓存清理操作。
  4. 清理完成后，提示用户“缓存已清理”，并可能建议用户重启应用或返回首页。

### 1.2. 帮助中心

- **目标**: 提供一个静态页面，集中展示常见问题解答（FAQ）、功能使用指南等，帮助用户自助解决问题。
- **业务流程**:
  1. 用户通过“我的”页面或设置页面进入“帮助中心”。
  2. 页面以列表或折叠面板的形式展示常见问题分类（如“账户问题”、“交易问题”）。
  3. 用户点击问题后，展开显示详细的解答内容。
  4. （可选）页面顶部提供搜索框，用于快速查找相关问题。

### 1.3. 意见反馈

- **目标**: 提供一个官方渠道，让用户可以方便地提交使用问题或功能建议。
- **业务流程**:
  1. 用户进入“意见反馈”页面。
  2. 页面提供一个多行文本输入框供用户填写反馈内容，并可附带联系方式（如手机号，默认为当前用户手机号）。
  3. （可选）支持用户上传截图，以更清晰地描述问题。
  4. 用户点击“提交”后，前端将反馈内容发送至后端API。
  5. 提交成功后，给予用户“感谢您的反馈”等友好提示。

### 1.4. 关于我们

- **目标**: 展示应用的基本信息、版本号和公司信息，建立品牌信任。
- **业务流程**:
  1. 用户进入“关于我们”页面。
  2. 页面清晰地展示应用的Logo、当前版本号（如 V2.1.0）、公司全称、官网链接等信息。
  3. 提供“检查更新”按钮，点击后可触发应用的版本检查逻辑。

### 1.5. 隐私政策与服务条款

- **目标**: 提供法律要求的隐私政策和服务条款文档，保障用户知情权。
- **业务流程**:
  1. 用户在“设置”或“登录”页面可以找到“隐私政策”和“服务条款”的链接。
  2. 点击后，跳转到相应的静态内容展示页面，完整显示条款内容。

---

## 2. 接口定义 (API Definition)

| 功能描述 | HTTP 方法 | 路径 (Endpoint) | 请求体说明 | 响应体说明 |
| :--- | :--- | :--- | :--- | :--- |
| **提交意见反馈** | `POST` | `/api/v1/feedback` | `{"content": "...", "contact": "...", "images": ["url1", ...]} ` | `{"code": 0, "msg": "反馈成功"}` |
| **获取静态内容** | `GET` | `/api/v1/content/{key}` | `key` 为 `help`, `about`, `privacy`, `terms` | `{"code": 0, "data": {"content": "..."}}` |

*注：静态内容也可以直接内置于前端，不通过API获取，以简化实现。*

---

## 3. 相关页面 (Related Pages)

- **账户中心/设置主页**: `app/src/pages/profile/index.vue` (作为这些功能的入口)
- **帮助中心页**: `app/src/pages/support/help.vue`
- **意见反馈页**: `app/src/pages/support/feedback.vue`
- **关于我们页**: `app/src/pages/support/about.vue`
- **通用内容展示页**: `app/src/pages/support/content-viewer.vue` (用于显示隐私政策、服务条款等)

---

## 4. 测试用例 (Test Cases)

| 用例ID | 场景描述 | 测试步骤 | 预期结果 |
| :--- | :--- | :--- | :--- |
| `TC-SUP-001` | **清理缓存** | 1. 在App中进行一些操作产生缓存。 2. 进入设置页，点击“清理缓存”并确认。 | 提示“清理成功”，重新进入之前的页面时数据被重新加载。 |
| `TC-SUP-002` | **提交意见反馈** | 1. 进入“意见反馈”页，填写内容。 2. 点击“提交”。 | 提示“提交成功”，后端数据库能查到对应的反馈记录。 |
| `TC-SUP-003` | **查看帮助中心** | 1. 进入“帮助中心”页。 2. 点击一个问题。 | 对应问题的答案被正确展开显示。 |
| `TC-SUP-004` | **检查版本更新** | 1. 进入“关于我们”页。 2. 点击“检查更新”。 | 触发版本检查逻辑，并根据结果给出相应提示（“已是最新版本”或“发现新版本”）。 |

---

## 5. 注意事项 (Notes/Caveats)

- **内容管理**: “帮助中心”、“隐私政策”等静态页面的内容，后端应提供一个简单的后台管理界面，方便运营人员随时更新，而无需修改前端代码重新发布。
- **用户体验**: “清理缓存”功能应谨慎使用，并向用户解释清楚可能带来的影响（如需要重新登录、加载数据变慢等）。
