import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { IInstrumentSelectItem } from '@/types/instrument'
import { getInstrumentSelectList } from '@/api/instrument'
import { ExchangeMap } from '@/types/instrument'

/**
 * 按交易所和产品分组的数据结构
 */
interface GroupedInstruments {
  exchanges: {
    value: string
    label: string
  }[]
  productsByExchange: Record<string, {
    value: string
    label: string
  }[]>
  contractsByProduct: Record<string, {
    value: string
    label: string
    instrumentData: IInstrumentSelectItem
  }[]>
  instrumentsById: Record<number, IInstrumentSelectItem>
  instrumentsByInstrumentId: Record<string, IInstrumentSelectItem>
}

export const useInstrumentStore = defineStore('instrument', () => {
  // 状态
  const instruments = ref<IInstrumentSelectItem[]>([])
  const isLoading = ref(false)
  const isLoaded = ref(false)
  const lastLoadTime = ref<number>(0)
  const error = ref<string | null>(null)

  // 缓存时间（5分钟）
  const CACHE_DURATION = 5 * 60 * 1000

  // 分组数据缓存
  const groupedData = ref<GroupedInstruments>({
    exchanges: [],
    productsByExchange: {},
    contractsByProduct: {},
    instrumentsById: {},
    instrumentsByInstrumentId: {}
  })

  // 计算属性
  const isDataFresh = computed(() => {
    return isLoaded.value && (Date.now() - lastLoadTime.value) < CACHE_DURATION
  })

  /**
   * 加载所有合约数据
   */
  async function loadInstruments(forceRefresh = false) {
    // 如果数据是新鲜的且不强制刷新，直接返回
    if (isDataFresh.value && !forceRefresh) {
      return { success: true, data: instruments.value }
    }

    if (isLoading.value) {
      // 如果正在加载，等待加载完成
      return new Promise((resolve) => {
        const checkLoading = () => {
          if (!isLoading.value) {
            resolve({ success: isLoaded.value, data: instruments.value })
          } else {
            setTimeout(checkLoading, 100)
          }
        }
        checkLoading()
      })
    }

    try {
      isLoading.value = true
      error.value = null

      console.log('开始加载合约数据...')
      const response = await getInstrumentSelectList()
      
      if (response.code === 0 && response.data) {
        instruments.value = response.data
        lastLoadTime.value = Date.now()
        isLoaded.value = true
        
        // 构建分组数据
        buildGroupedData()
        
        console.log(`合约数据加载成功，共 ${instruments.value.length} 个合约`)
        return { success: true, data: instruments.value }
      } else {
        throw new Error(response.msg || '获取合约数据失败')
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : '加载合约数据失败'
      error.value = errorMsg
      console.error('加载合约数据失败:', err)
      return { success: false, error: errorMsg }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * 构建分组数据
   */
  function buildGroupedData() {
    const data: GroupedInstruments = {
      exchanges: [],
      productsByExchange: {},
      contractsByProduct: {},
      instrumentsById: {},
      instrumentsByInstrumentId: {}
    }

    // 1. 构建交易所列表
    data.exchanges = Object.entries(ExchangeMap).map(([value, label]) => ({
      value,
      label
    }))

    // 2. 构建ID映射
    const productSet = new Map<string, string>()
    
    for (const instrument of instruments.value) {
      // 建立ID映射
      data.instrumentsById[instrument.id] = instrument
      data.instrumentsByInstrumentId[instrument.instrument_id] = instrument
      
      // 构建产品分组
      const productKey = `${instrument.exchange_id}_${instrument.product_name}`
      if (!productSet.has(productKey)) {
        productSet.set(productKey, instrument.product_name)

        if (!data.productsByExchange[instrument.exchange_id]) {
          data.productsByExchange[instrument.exchange_id] = []
        }

        data.productsByExchange[instrument.exchange_id].push({
          value: instrument.product_name,
          label: instrument.product_name
        })
      }
    }

    // 3. 构建合约分组
    for (const instrument of instruments.value) {
      const key = `${instrument.exchange_id}_${instrument.product_name}`
      if (!data.contractsByProduct[key]) {
        data.contractsByProduct[key] = []
      }

      data.contractsByProduct[key].push({
        value: instrument.instrument_id,
        label: instrument.instrument_id,
        instrumentData: instrument
      })
    }

    groupedData.value = data
  }

  /**
   * 根据ID获取合约
   */
  function getInstrumentById(id: number): IInstrumentSelectItem | null {
    return groupedData.value.instrumentsById[id] || null
  }

  /**
   * 根据instrument_id获取合约
   */
  function getInstrumentByInstrumentId(instrumentId: string): IInstrumentSelectItem | null {
    return groupedData.value.instrumentsByInstrumentId[instrumentId] || null
  }

  /**
   * 获取交易所列表
   */
  function getExchanges() {
    return groupedData.value.exchanges
  }

  /**
   * 获取指定交易所的产品列表
   */
  function getProductsByExchange(exchangeId: string) {
    return groupedData.value.productsByExchange[exchangeId] || []
  }

  /**
   * 获取指定交易所和产品的合约列表
   */
  function getContractsByProduct(exchangeId: string, productName: string) {
    const key = `${exchangeId}_${productName}`
    return groupedData.value.contractsByProduct[key] || []
  }

  /**
   * 搜索合约
   */
  function searchInstruments(keyword: string): IInstrumentSelectItem[] {
    if (!keyword.trim()) return instruments.value

    const lowerKeyword = keyword.toLowerCase()
    return instruments.value.filter(instrument => 
      instrument.instrument_id.toLowerCase().includes(lowerKeyword) ||
      instrument.instrument_name.toLowerCase().includes(lowerKeyword) ||
      instrument.product_name.toLowerCase().includes(lowerKeyword)
    )
  }

  /**
   * 清除缓存
   */
  function clearCache() {
    instruments.value = []
    isLoaded.value = false
    lastLoadTime.value = 0
    error.value = null
    groupedData.value = {
      exchanges: [],
      productsByExchange: {},
      contractsByProduct: {},
      instrumentsById: {},
      instrumentsByInstrumentId: {}
    }
  }

  /**
   * 获取分组数据（用于组件）
   */
  function getGroupedData() {
    return groupedData.value
  }

  /**
   * 确保数据已加载（如果没有则自动加载）
   */
  async function ensureDataLoaded() {
    if (!isLoaded.value || !isDataFresh.value) {
      await loadInstruments()
    }
  }

  /**
   * 获取所有合约数据（确保已加载）
   */
  async function getAllInstruments() {
    await ensureDataLoaded()
    return instruments.value
  }

  /**
   * 安全获取合约（自动加载数据）
   */
  async function safeGetInstrumentById(id: number): Promise<IInstrumentSelectItem | null> {
    await ensureDataLoaded()
    return getInstrumentById(id)
  }

  /**
   * 安全获取合约（自动加载数据）
   */
  async function safeGetInstrumentByInstrumentId(instrumentId: string): Promise<IInstrumentSelectItem | null> {
    await ensureDataLoaded()
    return getInstrumentByInstrumentId(instrumentId)
  }

  return {
    // 状态
    instruments,
    isLoading,
    isLoaded,
    error,
    isDataFresh,

    // 方法
    loadInstruments,
    getInstrumentById,
    getInstrumentByInstrumentId,
    getExchanges,
    getProductsByExchange,
    getContractsByProduct,
    searchInstruments,
    clearCache,
    getGroupedData,
    ensureDataLoaded,
    getAllInstruments,
    safeGetInstrumentById,
    safeGetInstrumentByInstrumentId
  }
}, {
  persist: {
    key: 'instrument-store',
    storage: {
      getItem: uni.getStorageSync,
      setItem: uni.setStorageSync,
    },
    paths: ['instruments', 'isLoaded', 'lastLoadTime', 'groupedData']
  }
})
